pub fn generate_sbe_bbo_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@bestBidAsk", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_ws_bbo_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@bookTicker", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream.binance.com:443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_sbe_trade_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@trade", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_sbe_depth_20_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@depth20", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_ws_depth_5_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@depth5@100ms", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_sbe_depth_diff_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@depth", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_ws_depth_diff_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@depth@100ms", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_market_data_ws_url_for_perf() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    // SBE格式使用 bestBidAsk 流名称（等同于 JSON 的 bookTicker）
    let streams: Vec<String> = symbols
        .iter()
        .filter(|symbol| !symbol.contains("btc"))
        .map(|symbol| format!("{}@bestBidAsk", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream-sbe.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}

pub fn generate_order_url() -> String {
    "wss://ws-api.binance.com:443/ws-api/v3".to_string()
}

pub fn generate_ws_agg_trades_url() -> String {
    let symbols = crate::engine::trading_pair::TradingPair::all_symbols();

    let streams: Vec<String> = symbols
        .iter()
        .map(|symbol| format!("{}@aggTrade", symbol.to_lowercase()))
        .collect();

    format!(
        "wss://stream.binance.com:9443/stream?streams={}",
        streams.join("/")
    )
}
