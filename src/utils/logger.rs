#![allow(dead_code)]
#![allow(clippy::missing_safety_doc)]
#![allow(static_mut_refs)]

use core::fmt;
use std::fmt::Write;

const BUF_SIZE: usize = 2048;

pub struct LogBuffer {
    buf: [u8; BUF_SIZE],
    pos: usize,
}

impl LogBuffer {
    const fn new() -> Self {
        Self {
            buf: [0; BUF_SIZE],
            pos: 0,
        }
    }

    #[inline(always)]
    fn ensure_capacity(&mut self, len: usize) {
        if self.pos + len > BUF_SIZE {
            self.flush();
        }
    }

    #[inline(always)]
    pub fn write_bytes(&mut self, bytes: &[u8]) {
        self.ensure_capacity(bytes.len());
        let end = self.pos + bytes.len();
        self.buf[self.pos..end].copy_from_slice(bytes);
        self.pos = end;
    }

    #[inline(always)]
    fn flush(&mut self) {
        if self.pos == 0 {
            return;
        }
        unsafe {
            libc::write(1, self.buf.as_ptr() as *const _, self.pos);
        }
        self.pos = 0;
    }
}

impl Write for LogBuffer {
    #[inline(always)]
    fn write_str(&mut self, s: &str) -> fmt::Result {
        self.write_bytes(s.as_bytes());
        Ok(())
    }
}

// Global single‑thread buffer --------------------------------------------------------------
static mut LOGBUF: LogBuffer = LogBuffer::new();

#[inline(always)]
pub fn buf() -> &'static mut LogBuffer {
    unsafe { &mut LOGBUF }
}

#[inline(always)]
pub fn log_write(args: fmt::Arguments) {
    let _ = buf().write_fmt(args);
}
#[inline(always)]
pub fn flush() {
    buf().flush();
}

// Fast timestamp prefix cache (single thread) ----------------------------------------------
static mut SEC_CACHE: u64 = 0;
static mut PREFIX: [u8; 22] = [0; 22]; // "[YYYY‑MM‑DD HH:MM:SS." (21) + null

#[inline(always)]
fn write_two(buf: &mut [u8], off: usize, v: u8) {
    buf[off] = b'0' + v / 10;
    buf[off + 1] = b'0' + v % 10;
}

#[inline(always)]
fn update_prefix(secs: i64) {
    // Fast Gregorian calendar conversion (Howard Hinnant, no chrono).
    // All math in i64 to allow negatives during conversion.
    let days = secs as i64 / 86_400;
    let sod = secs as i64 % 86_400;

    // civil_from_days
    let z = days + 719_468;
    let era = if z >= 0 {
        z / 146_097
    } else {
        (z - 146_096) / 146_097
    };
    let doe = z - era * 146_097; // [0, 146096]
    let yoe = (doe - doe / 1460 + doe / 36524 - doe / 146096) / 365;
    let y = yoe + era * 400; // year
    let doy = doe - (365 * yoe + yoe / 4 - yoe / 100); // [0, 365]
    let mp = (5 * doy + 2) / 153; // [0, 11]
    let d = doy - (153 * mp + 2) / 5 + 1; // day   1‥31
    let m = mp + if mp < 10 { 3 } else { -9 }; // month 1‥12
    let year = y + (m <= 2) as i64; // adjust year

    let hh = sod / 3600;
    let mm = (sod % 3600) / 60;
    let ss = sod % 60;

    unsafe {
        let p = &mut PREFIX;
        p[0] = b'[';
        // 年
        let yr = year as u32;
        p[1] = b'0' + ((yr / 1000) % 10) as u8;
        p[2] = b'0' + ((yr / 100) % 10) as u8;
        p[3] = b'0' + ((yr / 10) % 10) as u8;
        p[4] = b'0' + (yr % 10) as u8;
        p[5] = b'-';
        write_two(p, 6, m as u8);
        p[8] = b'-';
        write_two(p, 9, d as u8);
        p[11] = b' ';
        write_two(p, 12, hh as u8);
        p[14] = b':';
        write_two(p, 15, mm as u8);
        p[17] = b':';
        write_two(p, 18, ss as u8);
        p[20] = b'.'; // 第 21 字节
    }
}

#[inline(always)]
fn write_timestamp(buf: &mut LogBuffer, secs: u64, micros: u32) {
    unsafe {
        if SEC_CACHE != secs {
            update_prefix(secs as i64);
            SEC_CACHE = secs;
        }
        buf.write_bytes(&PREFIX[..21]);
    }
    // microseconds zero‑padded
    let mut digits = [b'0'; 6];
    let mut m = micros;
    for i in (0..6).rev() {
        digits[i] = b'0' + (m % 10) as u8;
        m /= 10;
    }
    buf.write_bytes(&digits);
    buf.write_bytes(b"] ");
}

// ---------- MACROS ------------------------------------------------------------------------
#[macro_export]
macro_rules! log {
    ($($arg:tt)*) => {{
        $crate::logger::log_write(core::format_args!($($arg)*));
    }};
}

#[macro_export]
macro_rules! log_with_ts {
    ($($arg:tt)*) => {{
        let mut ts = libc::timespec { tv_sec: 0, tv_nsec: 0 };
        unsafe { libc::clock_gettime(libc::CLOCK_REALTIME, &mut ts); }
        $crate::logger::logger_single_write(ts, core::format_args!($($arg)*));
    }};
}

#[inline(always)]
pub fn logger_single_write(ts: libc::timespec, args: core::fmt::Arguments) {
    let secs = ts.tv_sec as u64;
    let micros = (ts.tv_nsec / 1_000) as u32;
    let b = crate::logger::buf();
    write_timestamp(b, secs, micros);
    b.write_fmt(args).ok();
}

#[macro_export]
macro_rules! logln {
    ($($arg:tt)*) => {{
        let mut ts = libc::timespec { tv_sec: 0, tv_nsec: 0 };
        unsafe { libc::clock_gettime(libc::CLOCK_REALTIME, &mut ts); }
        $crate::logger::logger_single_write(ts, core::format_args!($($arg)*));
        $crate::logger::buf().write_bytes(b"\n");
    }};
}

#[macro_export]
macro_rules! flush_logs {
    () => {
        $crate::logger::flush();
    };
}
