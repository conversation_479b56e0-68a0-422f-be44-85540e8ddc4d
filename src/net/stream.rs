use std::io::{self, IoSlice, Read, Write};

use mio::net::TcpStream;
use rustls::ClientConnection;

use super::{<PERSON><PERSON><PERSON>, ErrorKind as Kind, utils::circular_buffer::CircularBuffer};
use crate::Result;

pub struct TlsStream {
    sock: TcpStream,
    client: ClientConnection,
}

enum ReadPlainTextResult {
    BufferIsFull(usize),
    WouldBlock(usize),
    ConnectionClose,
}

impl TlsStream {
    pub fn new(sock: TcpStream, client: ClientConnection) -> Self {
        Self { sock, client }
    }

    pub fn socket_mut(&mut self) -> &mut TcpStream {
        &mut self.sock
    }

    fn read_plaintext<const N: usize>(
        &mut self,
        buf: &mut CircularBuffer<N>,
    ) -> Result<ReadPlainTextResult> {
        if buf.is_full() {
            return Ok(ReadPlainTextResult::BufferIsFull(buf.len()));
        }
        let state = self.client.process_new_packets()?;
        if state.peer_has_closed() {
            return Ok(ReadPlainTextResult::ConnectionClose);
        }
        if state.plaintext_bytes_to_read() == 0 {
            return Ok(ReadPlainTextResult::WouldBlock(0));
        }
        let mut total_len = 0;
        loop {
            let (first, second) = buf.as_mut_slices();
            match self.client.reader().read(first) {
                Ok(0) => return Ok(ReadPlainTextResult::WouldBlock(0)),
                Ok(len) => {
                    if len == first.len() && !second.is_empty() {
                        match self.client.reader().read(second) {
                            Ok(0) => return Ok(ReadPlainTextResult::WouldBlock(0)),
                            Ok(second_len) => {
                                buf.advance_write(len + second_len);
                                total_len += len + second_len;
                            }
                            Err(e) => {
                                if e.kind() != io::ErrorKind::WouldBlock {
                                    return Err(Error::new(
                                        Kind::Io(e),
                                        "Error when reading plaintext second time.",
                                    ));
                                }
                                buf.advance_write(len);
                                return Ok(ReadPlainTextResult::WouldBlock(total_len));
                            }
                        }
                    } else {
                        buf.advance_write(len);
                        total_len += len;
                    }
                }
                Err(e) => {
                    if e.kind() != io::ErrorKind::WouldBlock {
                        return Err(Error::new(
                            Kind::Io(e),
                            "Error when reading plaintext first time.",
                        ));
                    }
                    return Ok(ReadPlainTextResult::WouldBlock(total_len));
                }
            }
        }
    }

    pub fn is_handshake_complete(&self) -> bool {
        !self.client.is_handshaking()
    }

    /// Reads data from the connection into the buffer.
    /// Returns None if the socket would block.
    /// Returns Some(0) if the connection is closed.
    /// Returns Some(len) if the buffer is not full.
    /// Returns Err if there is an error.
    pub fn read_buf<const N: usize>(
        &mut self,
        buf: &mut CircularBuffer<N>,
    ) -> Result<Option<usize>> {
        self.read_plaintext(buf)?;
        loop {
            let mut tls_would_block = false;
            match self.client.read_tls(&mut self.sock) {
                Ok(_) => (),
                Err(e) if e.kind() == io::ErrorKind::WouldBlock => tls_would_block = true,
                Err(e) => {
                    return Err(Error::new(Kind::Io(e), "Error when reading from TLS."));
                }
            };
            match self.read_plaintext(buf)? {
                ReadPlainTextResult::BufferIsFull(len) => return Ok(Some(len)),
                ReadPlainTextResult::WouldBlock(len) => {
                    if tls_would_block {
                        if len == 0 {
                            return Ok(None);
                        }
                        return Ok(Some(len));
                    }
                }
                ReadPlainTextResult::ConnectionClose => return Ok(Some(0)),
            }
        }
    }

    #[inline(always)]
    pub fn write_buf<const N: usize>(&mut self, buf: &mut CircularBuffer<N>) -> Result<()> {
        if !buf.is_empty() {
            let (first, second) = buf.as_slices();
            let mut writer = self.client.writer();
            let bufs = &mut [IoSlice::new(first), IoSlice::new(second)];
            match writer.write_vectored(bufs) {
                Ok(len) => {
                    buf.advance_and_commit_read(len);
                }
                Err(e) => {
                    if e.kind() != io::ErrorKind::WouldBlock {
                        return Err(Error::new(Kind::Io(e), "Error writing to TLS."));
                    }
                }
            }
            writer.flush()?;
        }
        match self.client.write_tls(&mut self.sock) {
            Ok(_) => {}
            Err(e) => {
                if e.kind() == io::ErrorKind::WouldBlock {
                    return Ok(());
                }
                return Err(Error::new(Kind::Io(e), "Error writing to connection."));
            }
        }
        Ok(())
    }

    pub fn shutdown(&mut self) -> Result<()> {
        self.sock.shutdown(std::net::Shutdown::Both)?;
        Ok(())
    }
}
