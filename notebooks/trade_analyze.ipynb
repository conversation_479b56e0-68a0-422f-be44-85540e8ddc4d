{"cells": [{"cell_type": "code", "execution_count": 10, "id": "d7736279", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "# pd.options.display.datetime_format = \"%Y-%m-%d %H:%M:%S.%f\"\n", "pd.options.display.precision = 6          # 浮点同时保留 6 位小数\n", "pd.set_option('display.max_rows', None)      # 行数不限\n", "pd.set_option('display.max_columns', None)   # 列数不限\n", "\n", "# 宽度相关\n", "pd.set_option('display.width', None)         # 根据终端自动换行\n", "pd.set_option('display.max_colwidth', None)  # 列内容（字符串）长度不限\n", "\n", "# partiusdt= pd.read_parquet(\"../data/data/partiusdt_trades_20250701_121910.parquet\")\n", "# partiusdc = pd.read_parquet(\"../data/data/partiusdc_trades_20250701_121910.parquet\")\n", "# bmtusdt = pd.read_parquet(\"../data/data/bmtusdt_trades_20250701_121910.parquet\")\n", "trade = pd.read_parquet(\"../data/data/data/gunusdc_trades_20250705_015733.parquet\")\n", "bbo = pd.read_parquet(\"../data/data/data/gunusdc_bbo_20250705_015733.parquet\")"]}, {"cell_type": "code", "execution_count": 11, "id": "fe1ff0e1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                 timestamp   symbol  trade_id    price  quantity  \\\n", "30 2025-07-05 01:56:07.061  GUNUSDC    762618  0.03343    6498.0   \n", "31 2025-07-05 01:56:07.062  GUNUSDC    762619  0.03347     290.0   \n", "\n", "   buyer_order_id seller_order_id              trade_time  is_buyer_maker  \n", "30           None            None 2025-07-05 01:56:07.061           False  \n", "31           None            None 2025-07-05 01:56:07.062           False  \n", "                   timestamp   symbol  bid_price  bid_qty  ask_price  ask_qty  \\\n", "1477 2025-07-05 01:56:06.605  GUNUSDC    0.03333   9675.0    0.03340  13585.0   \n", "1478 2025-07-05 01:56:06.607  GUNUSDC    0.03334   4762.0    0.03340  13585.0   \n", "1479 2025-07-05 01:56:06.718  GUNUSDC    0.03336   3223.0    0.03340  13585.0   \n", "1480 2025-07-05 01:56:06.719  GUNUSDC    0.03337   4752.0    0.03340  13585.0   \n", "1481 2025-07-05 01:56:06.798  GUNUSDC    0.03337   4752.0    0.03340   5000.0   \n", "1482 2025-07-05 01:56:06.798  GUNUSDC    0.03337   4752.0    0.03342   5000.0   \n", "1483 2025-07-05 01:56:06.798  GUNUSDC    0.03337   4752.0    0.03342  13587.0   \n", "1484 2025-07-05 01:56:06.916  GUNUSDC    0.03337   4752.0    0.03342   8587.0   \n", "1485 2025-07-05 01:56:06.919  GUNUSDC    0.03337   4752.0    0.03343  21209.0   \n", "1486 2025-07-05 01:56:06.919  GUNUSDC    0.03337   4752.0    0.03343   6498.0   \n", "1487 2025-07-05 01:56:06.921  GUNUSDC    0.03337   4752.0    0.03343  15086.0   \n", "1488 2025-07-05 01:56:06.921  GUNUSDC    0.03337   4752.0    0.03343   6498.0   \n", "1489 2025-07-05 01:56:07.062  GUNUSDC    0.03337   4752.0    0.03347  14722.0   \n", "1490 2025-07-05 01:56:07.063  GUNUSDC    0.03337   4752.0    0.03347  23310.0   \n", "1491 2025-07-05 01:56:07.063  GUNUSDC    0.03337   4752.0    0.03347   8588.0   \n", "1492 2025-07-05 01:56:07.063  GUNUSDC    0.03337   4752.0    0.03347   8298.0   \n", "1493 2025-07-05 01:56:07.112  GUNUSDC    0.03337   4752.0    0.03348  14757.0   \n", "1494 2025-07-05 01:56:07.113  GUNUSDC    0.03337   4752.0    0.03348  23304.0   \n", "1495 2025-07-05 01:56:07.341  GUNUSDC    0.03337   4752.0    0.03347   5000.0   \n", "1496 2025-07-05 01:56:07.344  GUNUSDC    0.03337   4752.0    0.03346   8547.0   \n", "1497 2025-07-05 01:56:07.368  GUNUSDC    0.03337   4752.0    0.03347   5000.0   \n", "1498 2025-07-05 01:56:07.372  GUNUSDC    0.03337  19436.0    0.03347   5000.0   \n", "1499 2025-07-05 01:56:07.772  GUNUSDC    0.03337  70406.0    0.03347   5000.0   \n", "1500 2025-07-05 01:56:07.784  GUNUSDC    0.03338  59956.0    0.03347   5000.0   \n", "1501 2025-07-05 01:56:07.786  GUNUSDC    0.03339   4897.0    0.03347   5000.0   \n", "1502 2025-07-05 01:56:07.911  GUNUSDC    0.03341   3223.0    0.03347   5000.0   \n", "1503 2025-07-05 01:56:07.912  GUNUSDC    0.03342   4744.0    0.03347   5000.0   \n", "1504 2025-07-05 01:56:07.957  GUNUSDC    0.03342   4744.0    0.03346   8527.0   \n", "1505 2025-07-05 01:56:07.958  GUNUSDC    0.03342   4744.0    0.03345   5000.0   \n", "\n", "      update_id  \n", "1477   69346860  \n", "1478   69346862  \n", "1479   69346866  \n", "1480   69346867  \n", "1481   69346870  \n", "1482   69346872  \n", "1483   69346873  \n", "1484   69346880  \n", "1485   69346882  \n", "1486   69346883  \n", "1487   69346884  \n", "1488   69346885  \n", "1489   69346902  \n", "1490   69346903  \n", "1491   69346904  \n", "1492   69346906  \n", "1493   69346914  \n", "1494   69346915  \n", "1495   69346928  \n", "1496   69346931  \n", "1497   69346933  \n", "1498   69346934  \n", "1499   69346939  \n", "1500   69346942  \n", "1501   69346943  \n", "1502   69346947  \n", "1503   69346948  \n", "1504   69346949  \n", "1505   69346952  \n"]}], "source": ["start = \"2025-07-05 01:56:06.000\"\n", "end = \"2025-07-05 01:56:08.000\"\n", "# partiusdt = partiusdt[(partiusdt['timestamp'] > \"2025-07-01 20:15:05.000\") & (partiusdt['timestamp'] < \"2025-07-01 20:15:06.000\")]\n", "trade = trade[(trade['timestamp'] > start) & (trade['timestamp'] < end)]\n", "print(trade)\n", "bbo = bbo[(bbo['timestamp'] > start) & (bbo['timestamp'] < end)]\n", "print(bbo)\n", "# bmtusdt = bmtusdt[(bmtusdt['timestamp'] > \"2025-07-01 12:15:05.000\") & (bmtusdt['timestamp'] < \"2025-07-01 12:17:36.000\")]\n", "# bmtusdc = bmtusdc[(bmtusdc['timestamp'] > \"2025-07-01 12:15:35.000\") & (bmtusdc['timestamp'] < \"2025-07-01 12:17:36.000\")]\n", "\n", "# print(bmtusdt\n", "# print(bmtusdc)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}